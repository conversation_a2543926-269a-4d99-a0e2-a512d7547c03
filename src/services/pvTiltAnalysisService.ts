import { PVModule } from '../types/project';
import { ProjectData, ProjectHourlyData, PVModuleTiltAnalysis } from '../types/projectData';
import { IrradianceHourlyData } from '../types/database';
import { getIrradianceDetail } from './irradianceService';
import { updateProjectSync } from './projectSyncService';
import { special } from '../config/settings';

/**
 * 倾角分析结果数据结构
 */
export interface TiltAnalysisResult {
  tiltAngle: number;                    // 倾角（度）
  annualGeneration: number;             // 年发电量 (kWh)
  annualBenefit: number;                // 年光伏收益 (JPY)
  generationPerKW: number;              // 每千瓦发电量 (kWh/kW)
  benefitPerKW: number;                 // 每千瓦收益 (JPY/kW)
  pvReturnRate: number;                 // 光伏年收益率 (%)
}

/**
 * 倾角分析完整结果
 */
export interface TiltAnalysisData {
  results: TiltAnalysisResult[];        // 0-90度每度的分析结果
  optimalTiltAngle: number;             // 最优倾角
  maxAnnualGeneration: number;          // 最大年发电量
  maxAnnualBenefit: number;             // 最大年收益
}

/**
 * 进度回调函数类型
 */
export type ProgressCallback = (progress: number, message: string) => void;

/**
 * 角度转弧度
 */
const degreesToRadians = (degrees: number): number => {
  return degrees * Math.PI / 180;
};

/**
 * 朝向转换为角度（与正南的夹角）
 */
const orientationToAngle = (orientation: string): number => {
  const orientationMap: { [key: string]: number } = {
    'south': 0,      // 正南
    'southeast': -45, // 东南
    'east': -90,     // 正东
    'northeast': -135, // 东北
    'north': 180,    // 正北
    'northwest': 135, // 西北
    'west': 90,      // 正西
    'southwest': 45,  // 西南
  };
  return orientationMap[orientation] || 0;
};

/**
 * 计算指定倾角下的光伏发电量
 */
const calculatePVGenerationForTilt = async (
  project: ProjectData,
  selectedModules: PVModule[],
  tiltAngle: number
): Promise<{ generation: number; benefit: number }> => {
  try {
    // 获取光照数据
    const irradianceData = await getIrradianceDetail(project.irradianceDataId);
    console.log('获取到的光照数据:', irradianceData);

    if (!irradianceData) {
      throw new Error('无法获取光照数据');
    }

    // 检查数据结构，支持两种格式：hourlyData 或 data
    let hourlyData = irradianceData.hourlyData || irradianceData.data;
    if (!hourlyData || !Array.isArray(hourlyData) || hourlyData.length === 0) {
      throw new Error('光照数据格式不正确或为空');
    }

    console.log('光照数据条数:', hourlyData.length);

    let totalGeneration = 0;
    let totalBenefit = 0;

    // 计算逆变器效率，使用与原项目分析相同的方法
    const inverterEfficiency = project.inverters.length > 0
      ? project.inverters.reduce((sum, inverter) => sum + inverter.efficiency * inverter.quantity, 0) /
        project.inverters.reduce((sum, inverter) => sum + inverter.quantity, 0) / 100
      : special.projects.analysis.defaultEfficiency.inverter;

    // 创建8760个小时的数据点（与原项目分析保持一致）
    const hourlyDataPoints = [];
    for (let month = 1; month <= 12; month++) {
      const daysInMonth = new Date(2023, month, 0).getDate();
      for (let day = 1; day <= daysInMonth; day++) {
        for (let hour = 0; hour < 24; hour++) {
          hourlyDataPoints.push({ month, day, hour });
        }
      }
    }

    // 创建光照数据的快速查找映射
    const irradianceMap = new Map<string, any>();
    for (const irr of hourlyData) {
      const key = `${irr.month}-${irr.day}-${irr.hour}`;
      irradianceMap.set(key, irr);
    }

    let processedCount = 0;
    const totalPoints = hourlyDataPoints.length;

    // 遍历每小时数据点
    for (const hourPoint of hourlyDataPoints) {
      // 光照数据使用1-24小时制，而项目数据使用0-23小时制
      // 需要将项目数据的小时值+1来匹配光照数据
      const irradianceHour = irradianceMap.get(`${hourPoint.month}-${hourPoint.day}-${hourPoint.hour + 1}`) ||
                            (hourPoint.hour === 23 ? irradianceMap.get(`${hourPoint.month}-${hourPoint.day}-24`) : null);

      if (!irradianceHour) {
        processedCount++;
        continue; // 跳过没有光照数据的小时
      }

      let hourlyGeneration = 0;

      // 遍历选中的光伏组件
      for (const module of selectedModules) {
        // 计算组件效率（百分比转为小数）
        const efficiency = module.efficiency / 100;

        // 获取朝向角（与正南的夹角）
        const orientationAngle = orientationToAngle(module.orientation);

        // 获取太阳高度角和方位角
        const sunHeight = irradianceHour.sunHeight;
        const sunAngle = irradianceHour.sunAngle;

        // 计算辐射量
        const diffuseRadiation = irradianceHour.diffuseHorizontalIrradiance;
        const directNormalIrradiance = irradianceHour.directNormalIrradiance;

        // 直射辐射计算
        const sunHeightRad = degreesToRadians(sunHeight);
        const installAngleRad = degreesToRadians(tiltAngle); // 使用指定的倾角
        const sunAngleRad = degreesToRadians(sunAngle);
        const orientationAngleRad = degreesToRadians(orientationAngle);

        let directRadiation = 0;
        if (sunHeight > 0) {
          // 计算太阳光线与光伏板法线之间的夹角余弦值
          const cosTheta = Math.sin(sunHeightRad) * Math.cos(installAngleRad) +
                          Math.cos(sunHeightRad) * Math.sin(installAngleRad) * Math.cos(sunAngleRad - orientationAngleRad);

          if (cosTheta > 0) {
            directRadiation = directNormalIrradiance * cosTheta;
          }
        }

        // 总辐射量 (W/m²)
        const totalRadiation = diffuseRadiation + Math.max(0, directRadiation);

        // 计算该组件的发电量 (kWh)
        const moduleArea = module.area * module.quantity;
        const moduleGeneration = (totalRadiation * moduleArea * efficiency * inverterEfficiency) / 1000;

        hourlyGeneration += moduleGeneration;
      }

      totalGeneration += hourlyGeneration;

      // 计算收益（从项目分析结果中获取电价信息）
      let electricityConsumption = 0;
      let electricityPrice = special.projects.analysis.defaultPrice; // 使用配置中的默认电价
      let gridFeedInPrice = special.projects.analysis.defaultGridFeedInPrice; // 使用配置中的默认上网电价

      // 简化项目数据查找，避免重复查找
      if (project.analysisResults?.hourlyData) {
        // 使用更高效的查找方式
        const projectHour = project.analysisResults.hourlyData.find(h =>
          h.month === hourPoint.month &&
          h.day === hourPoint.day &&
          h.hour === hourPoint.hour
        );
        if (projectHour) {
          electricityConsumption = projectHour.electricityConsumption;
          electricityPrice = projectHour.electricityPrice || electricityPrice;
          gridFeedInPrice = projectHour.gridFeedInPrice || gridFeedInPrice;
        }
      }

      // 计算该小时的收益（与原项目分析保持一致）
      let hourlyBenefit = 0;
      if (hourlyGeneration <= electricityConsumption) {
        // 全部自用
        hourlyBenefit = hourlyGeneration * electricityPrice;
      } else {
        // 部分自用，部分上网
        hourlyBenefit = electricityConsumption * electricityPrice +
                       (hourlyGeneration - electricityConsumption) * gridFeedInPrice;
      }

      totalBenefit += hourlyBenefit;
      processedCount++;
    }

    console.log(`处理了 ${processedCount}/${totalPoints} 个小时数据点`);

    return {
      generation: parseFloat(totalGeneration.toFixed(3)),
      benefit: parseFloat(totalBenefit.toFixed(1))
    };
  } catch (error) {
    console.error('计算倾角发电量时出错:', error);
    throw error;
  }
};

/**
 * 生成组件组合的唯一键
 */
const generateCombinationKey = (moduleIds: string[]): string => {
  return moduleIds.sort().join('-');
};

/**
 * 保存倾角分析结果到项目数据
 */
export const saveTiltAnalysisToProject = async (
  project: ProjectData,
  selectedModuleIds: string[],
  analysisData: TiltAnalysisData
): Promise<ProjectData> => {
  try {
    console.log('保存倾角分析结果到项目数据');

    // 生成组合键
    const combinationKey = generateCombinationKey(selectedModuleIds);

    // 创建倾角分析数据
    const tiltAnalysis: PVModuleTiltAnalysis = {
      moduleIds: selectedModuleIds,
      analysisDate: new Date().toISOString(),
      results: analysisData.results,
      optimalTiltAngle: analysisData.optimalTiltAngle,
      maxAnnualGeneration: analysisData.maxAnnualGeneration,
      maxAnnualBenefit: analysisData.maxAnnualBenefit
    };

    // 更新项目数据
    const updatedProject: ProjectData = {
      ...project,
      analysisResults: {
        ...project.analysisResults!,
        pvTiltAnalysis: {
          ...project.analysisResults?.pvTiltAnalysis,
          [combinationKey]: tiltAnalysis
        }
      },
      updatedAt: new Date().toISOString()
    };

    // 保存到服务器和本地
    const savedProject = await updateProjectSync(updatedProject.id, updatedProject);
    console.log('倾角分析结果已保存');

    return savedProject as ProjectData;
  } catch (error) {
    console.error('保存倾角分析结果失败:', error);
    throw error;
  }
};

/**
 * 从项目数据中获取倾角分析结果
 */
export const getTiltAnalysisFromProject = (
  project: ProjectData,
  selectedModuleIds: string[]
): PVModuleTiltAnalysis | null => {
  const combinationKey = generateCombinationKey(selectedModuleIds);
  return project.analysisResults?.pvTiltAnalysis?.[combinationKey] || null;
};

/**
 * 执行光伏倾角分析
 */
export const performTiltAnalysis = async (
  project: ProjectData,
  selectedModuleIds: string[],
  progressCallback?: ProgressCallback
): Promise<TiltAnalysisData> => {
  try {
    console.log('开始倾角分析，选中组件:', selectedModuleIds);
    console.log('项目光照数据ID:', project.irradianceDataId);

    // 获取选中的组件
    const selectedModules = project.pvModules.filter(module =>
      selectedModuleIds.includes(module.id)
    );

    if (selectedModules.length === 0) {
      throw new Error('未选择任何组件');
    }

    console.log('选中的组件数量:', selectedModules.length);

    // 计算总功率和总投资（与原PVAnalysisTab保持一致）
    const totalPower = selectedModules.reduce((sum, module) =>
      sum + module.power * module.quantity, 0) / 1000; // 转换为kW

    // 计算选中组件的投资成本
    const selectedModulesCost = selectedModules.reduce((sum, module) =>
      sum + module.price * module.quantity, 0);

    // 计算总光伏功率和其他投资成本
    const totalPVPower = project.pvModules.reduce((sum, module) =>
      sum + module.power * module.quantity, 0) / 1000; // 转换为kW

    const invertersCost = project.inverters.reduce((sum, inverter) =>
      sum + inverter.price * inverter.quantity, 0);

    const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) =>
      sum + item.price, 0);

    // 计算选中组件应分担的逆变器和其他投资成本（按装机功率比例分配）
    const powerRatio = totalPVPower > 0 ? totalPower / totalPVPower : 0;
    const allocatedInvertersCost = invertersCost * powerRatio;
    const allocatedOtherCost = otherInvestmentsCost * powerRatio;

    // 总投资 = 组件成本 + 分摊的逆变器成本 + 分摊的其他投资成本
    const totalInvestment = selectedModulesCost + allocatedInvertersCost + allocatedOtherCost;

    const results: TiltAnalysisResult[] = [];
    let maxGeneration = 0;
    let maxBenefit = 0;
    let optimalAngle = 0;

    // 对-90到90度每1度进行计算
    for (let angle = -90; angle <= 90; angle++) {
      const progress = ((angle + 90) / 180) * 100;
      const message = `正在计算倾角 ${angle}°...`;

      console.log(message);
      if (progressCallback) {
        progressCallback(progress, message);
      }

      try {
        const { generation, benefit } = await calculatePVGenerationForTilt(
          project,
          selectedModules,
          angle
        );

        const generationPerKW = totalPower > 0 ? generation / totalPower : 0;
        const benefitPerKW = totalPower > 0 ? benefit / totalPower : 0;
        const pvReturnRate = totalInvestment > 0 ? (benefit / totalInvestment) * 100 : 0;

        const result: TiltAnalysisResult = {
          tiltAngle: angle,
          annualGeneration: generation,
          annualBenefit: benefit,
          generationPerKW: parseFloat(generationPerKW.toFixed(1)),
          benefitPerKW: parseFloat(benefitPerKW.toFixed(1)),
          pvReturnRate: parseFloat(pvReturnRate.toFixed(1))
        };

        results.push(result);

        // 记录最大值和最优角度
        if (generation > maxGeneration) {
          maxGeneration = generation;
          optimalAngle = angle;
        }
        if (benefit > maxBenefit) {
          maxBenefit = benefit;
        }
      } catch (error) {
        console.error(`计算倾角 ${angle}° 时出错:`, error);
        throw new Error(`计算倾角 ${angle}° 时出错: ${(error as Error).message}`);
      }

      // 添加小延迟，避免阻塞UI
      if (angle % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    // 完成进度回调
    if (progressCallback) {
      progressCallback(100, '倾角分析完成');
    }

    console.log('倾角分析完成，最优角度:', optimalAngle);

    return {
      results,
      optimalTiltAngle: optimalAngle,
      maxAnnualGeneration: maxGeneration,
      maxAnnualBenefit: maxBenefit
    };
  } catch (error) {
    console.error('倾角分析失败:', error);
    throw error;
  }
};
